const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { generateOTP, saveOTP, verifyOTP } = require('../utils/OTPservice');
const sendEmail = require('../utils/emailService');
const Token = require('../models/Token');
const GoogleStrategy = require('passport-google-oauth2').Strategy;
const passport = require('passport');
const redis = require('redis');
const dotenv = require('dotenv');
dotenv.config();

// Redis client configuration
const redisClient = redis.createClient({
	url: process.env.REDIS_URL, // Your Redis public endpoint
	password: process.env.REDIS_PASSWORD, // Your Redis password if required
});

// Connect to Redis
redisClient.connect().catch(console.error);

// Redis event handlers
redisClient.on('error', (err) => {
	console.error('Redis Client Error:', err);
});

redisClient.on('connect', () => {
	console.log('Connected to Redis successfully');
});

redisClient.on('ready', () => {
	console.log('Redis client ready');
});

const registerUser = async (req, res) => {
	const { name, email, matricNumber, password } = req.body;

	try {
		// Validate input
		if (!name || !email || !matricNumber || !password) {
			return res.status(400).json({ message: 'All fields are required' });
		}

		// Check if user already exists (verified users only)
		const existingUser = await User.findOne({ email, isVerified: true });
		if (existingUser) {
			return res.status(400).json({ message: 'User already exists' });
		}

		// Check if email is already pending verification
		const pendingKey = `pending_${email}`;
		const existingPending = await redisClient.get(pendingKey);
		if (existingPending) {
			return res.status(400).json({
				message:
					'Email verification already pending. Please check your email or request a new code.',
				canResend: true,
				email: email,
			});
		}

		// Delete any existing unverified user with this email
		await User.deleteOne({ email, isVerified: false });

		// Hash the password
		const hashedPassword = await bcrypt.hash(password, 10);

		// Store registration data temporarily in Redis
		const registrationData = {
			name,
			email,
			matricNumber,
			password: hashedPassword,
			timestamp: Date.now(),
			attempts: 0,
			otp: otp,
			otpExpiry: Date.now() + 5 * 60 * 1000, // 5 minutes
		};

		// Store in Redis with 10-minute expiration
		await redisClient.setEx(pendingKey, 600, JSON.stringify(registrationData)); // 600 seconds = 10 minutes

		// Send OTP via email
		await sendEmail(
			email,
			'Email Verification - Document Management Platform',
			`Hello ${name},\n\nWelcome to our Document Management Platform!\n\nYour verification code is: ${otp}\n\nThis code expires in 5 minutes.\n\nIf you didn't request this, please ignore this email.`,
		);

		// Redis will automatically expire the key after 10 minutes
		// No need for setTimeout cleanup

		res.status(200).json({
			message:
				'Verification code sent to your email. Please verify to complete registration.',
			email: email,
			canResend: true,
		});

		console.log('Registration initiated for:', email);
	} catch (error) {
		console.error('Registration error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const verifyEmail = async (req, res) => {
	const { email, otp } = req.body;

	try {
		if (!email || !otp) {
			return res.status(400).json({ message: 'Email and OTP are required' });
		}

		const pendingKey = `pending_${email}`;
		const pendingData = await redisClient.get(pendingKey);

		if (!pendingData) {
			return res.status(400).json({
				message: 'No pending registration found. Please register again.',
				needsRegistration: true,
			});
		}

		const registrationData = JSON.parse(pendingData);

		// Check if OTP is expired
		if (Date.now() > registrationData.otpExpiry) {
			await redisClient.del(pendingKey);
			return res.status(400).json({
				message: 'Verification code expired. Please register again.',
				needsRegistration: true,
			});
		}

		// Check if OTP matches
		if (registrationData.otp !== otp) {
			registrationData.attempts++;

			// Allow max 3 attempts
			if (registrationData.attempts >= 3) {
				await redisClient.del(pendingKey);
				return res.status(400).json({
					message: 'Too many failed attempts. Please register again.',
					needsRegistration: true,
				});
			}

			// Update attempts in Redis
			await redisClient.setEx(
				pendingKey,
				600,
				JSON.stringify(registrationData),
			);

			return res.status(400).json({
				message: `Invalid verification code. ${
					3 - registrationData.attempts
				} attempts remaining.`,
				attemptsRemaining: 3 - registrationData.attempts,
			});
		}

		// Create the user now that email is verified
		const user = await User.create({
			name: registrationData.name,
			email: registrationData.email,
			matricNumber: registrationData.matricNumber,
			password: registrationData.password,
			isVerified: true, // Set as verified since we just verified the email
		});

		// Clean up pending registration from Redis
		await redisClient.del(pendingKey);

		// Generate JWT token for immediate login
		const token = jwt.sign(
			{ id: user._id, isAdmin: user.isAdmin },
			process.env.JWT_SECRET,
			{ expiresIn: '30d' },
		);

		// Save the token in the database
		const expiresAt = new Date();
		expiresAt.setDate(expiresAt.getDate() + 30);

		await Token.create({
			userId: user._id,
			token,
			type: 'access',
			device: req.headers['user-agent'] || 'unknown',
			expiresAt,
		});

		res.status(201).json({
			message: 'Email verified and registration completed successfully!',
			token,
			user: {
				id: user._id,
				name: user.name,
				email: user.email,
				matricNumber: user.matricNumber,
				isAdmin: user.isAdmin,
				superAdmin: user.superAdmin,
				documentToken: user.documentToken,
			},
		});

		console.log('User registered and verified:', user.email);
	} catch (error) {
		console.error('Email verification error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const resendVerificationCode = async (req, res) => {
	const { email } = req.body;

	try {
		if (!email) {
			return res.status(400).json({ message: 'Email is required' });
		}

		const pendingKey = `pending_${email}`;
		const pendingData = await redisClient.get(pendingKey);

		if (!pendingData) {
			return res.status(400).json({
				message: 'No pending registration found. Please register again.',
				needsRegistration: true,
			});
		}

		const registrationData = JSON.parse(pendingData);

		// Check rate limiting (prevent spam)
		const timeSinceLastSent = Date.now() - registrationData.timestamp;
		if (timeSinceLastSent < 60000) {
			// 1 minute cooldown
			return res.status(429).json({
				message: `Please wait ${Math.ceil(
					(60000 - timeSinceLastSent) / 1000,
				)} seconds before requesting a new code.`,
				canResend: false,
			});
		}

		// Generate new OTP
		const otp = generateOTP();
		registrationData.otp = otp;
		registrationData.otpExpiry = Date.now() + 5 * 60 * 1000;
		registrationData.timestamp = Date.now();
		registrationData.attempts = 0; // Reset attempts

		// Update in Redis with fresh 10-minute expiration
		await redisClient.setEx(pendingKey, 600, JSON.stringify(registrationData));

		// Send new OTP via email
		await sendEmail(
			email,
			'New Verification Code - Document Management Platform',
			`Hello ${registrationData.name},\n\nYour new verification code is: ${otp}\n\nThis code expires in 5 minutes.\n\nIf you didn't request this, please ignore this email.`,
		);

		res.status(200).json({
			message: 'New verification code sent to your email.',
			canResend: true,
		});

		console.log('Verification code resent for:', email);
	} catch (error) {
		console.error('Resend verification error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const loginUser = async (req, res) => {
	const { email, password } = req.body;

	try {
		const user = await User.findOne({ email });
		if (!user) return res.status(400).json({ message: 'Invalid credentials' });

		if (!user.isVerified) {
			// Check if there's a pending registration in Redis
			const pendingKey = `pending_${email}`;
			const pendingData = await redisClient.get(pendingKey);
			if (pendingData) {
				return res.status(400).json({
					message: 'Please verify your email to complete registration',
					needsVerification: true,
					email: email,
				});
			}

			return res.status(400).json({
				message: 'Account not verified. Please register again.',
				needsRegistration: true,
			});
		}

		const isMatch = await bcrypt.compare(password, user.password);
		if (!isMatch)
			return res.status(400).json({ message: 'Invalid credentials' });

		// Generate JWT token
		const token = jwt.sign(
			{ id: user._id, isAdmin: user.isAdmin },
			process.env.JWT_SECRET,
			{ expiresIn: '30d' },
		);

		// Save the token in the database
		const expiresAt = new Date();
		expiresAt.setDate(expiresAt.getDate() + 30);

		await Token.create({
			userId: user._id,
			token,
			type: 'access',
			device: req.headers['user-agent'] || 'unknown',
			expiresAt,
		});

		res.json({
			token,
			user: {
				id: user._id,
				name: user.name,
				email: user.email,
				isAdmin: user.isAdmin,
				superAdmin: user.superAdmin,
				documentToken: user.documentToken,
			},
		});
	} catch (error) {
		console.error('Login Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

// Verify OTP and Reset Password
const resetPassword = async (req, res) => {
	const { email, otp, newPassword } = req.body;

	try {
		// Check if user exists
		const user = await User.findOne({ email });
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Verify OTP
		await verifyOTP(user._id, otp);

		// Hash the new password
		const hashedPassword = await bcrypt.hash(newPassword, 10);

		// Update user's password
		await User.findByIdAndUpdate(user._id, { password: hashedPassword });

		res.status(200).json({
			success: true,
			message: 'Password reset successfully',
		});
	} catch (error) {
		console.error('Reset Password Error:', error);
		res.status(400).json({ message: error.message });
	}
};

const forgotPassword = async (req, res) => {
	const { email } = req.body;

	try {
		// Check if user exists and is verified
		const user = await User.findOne({ email, isVerified: true });
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Generate and save OTP
		const otp = generateOTP();
		await saveOTP(user._id, otp);

		// Send OTP via email
		await sendEmail(
			email,
			'Password Reset OTP - Document Management Platform',
			`Hello ${user.name},\n\nYour OTP for password reset is: ${otp}\n\nThis code expires in 5 minutes.\n\nIf you didn't request this, please ignore this email.`,
		);

		res.status(200).json({
			success: true,
			message: 'OTP sent successfully',
		});
	} catch (error) {
		console.error('Forgot Password Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const checkUserStatus = async (req, res) => {
	const authHeader = req.headers.authorization;

	if (!authHeader || !authHeader.startsWith('Bearer ')) {
		return res
			.status(401)
			.json({ message: 'Unauthorized - No token provided' });
	}

	const token = authHeader.split(' ')[1];

	try {
		// Verify JWT
		const decoded = jwt.verify(token, process.env.JWT_SECRET);

		// Check if token exists in database
		const tokenExists = await Token.findOne({
			userId: decoded.id,
			token: token,
			expiresAt: { $gt: new Date() },
		});

		if (!tokenExists) {
			return res.status(401).json({ message: 'Unauthorized - Invalid token' });
		}

		// Get user data
		const user = await User.findById(decoded.id).select(
			'-password -__v -createdAt -updatedAt',
		);

		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Return updated user status
		res.json({
			user: {
				id: user._id,
				name: user.name,
				email: user.email,
				isAdmin: user.isAdmin,
				superAdmin: user.superAdmin,
				documentToken: user.documentToken,
				documentsReceived: user.documentsReceived,
				isVerified: user.isVerified,
				matricNumber: user.matricNumber,
			},
		});
	} catch (error) {
		console.error('Status check error:', error);

		if (error.name === 'TokenExpiredError') {
			return res.status(401).json({ message: 'Token expired' });
		}

		if (error.name === 'JsonWebTokenError') {
			return res.status(401).json({ message: 'Invalid token' });
		}

		res.status(500).json({ message: 'Server error' });
	}
};

// Google Authentication Routes
const googleLogin = passport.authenticate('google', {
	scope: ['profile', 'email'],
});

const googleCallback = (req, res, next) => {
	passport.authenticate('google', async (err, user, info) => {
		if (err || !user) {
			console.error('Google authentication error:', err || 'No user returned');
			return res.redirect(
				`${process.env.FRONTEND_URL}/login?error=google_auth_failed`,
			);
		}

		req.logIn(user, async (err) => {
			if (err) {
				console.error('Session establishment error:', err);
				return next(err);
			}

			// Generate JWT token
			const token = jwt.sign(
				{ id: user._id, isAdmin: user.isAdmin },
				process.env.JWT_SECRET,
				{ expiresIn: '30d' },
			);

			// Save the token in the database
			await Token.create({
				userId: user._id,
				token,
				type: 'access',
				device: req.headers['user-agent'] || 'unknown',
				expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30-day expiration
			});

			// Redirect to frontend with token
			res.redirect(
				`${process.env.FRONTEND_URL}/google-auth-callback?token=${token}`,
			);
		});
	})(req, res, next);
};

const getUser = async (req, res) => {
	try {
		const token = req.headers.authorization?.split(' ')[1];
		if (!token) return res.status(401).json({ message: 'Unauthorized' });

		const decoded = jwt.verify(token, process.env.JWT_SECRET);
		const user = await User.findById(decoded.id).select('-password');

		if (!user) return res.status(404).json({ message: 'User not found' });

		res.json(user);
	} catch (err) {
		console.error(err);
		res.status(401).json({ message: 'Invalid token' });
	}
};

// Graceful shutdown handler
process.on('SIGINT', async () => {
	console.log('Shutting down gracefully...');
	await redisClient.quit();
	process.exit(0);
});

process.on('SIGTERM', async () => {
	console.log('Shutting down gracefully...');
	await redisClient.quit();
	process.exit(0);
});

module.exports = {
	registerUser,
	loginUser,
	verifyEmail,
	resendVerificationCode, // New function
	resetPassword,
	forgotPassword,
	checkUserStatus,
	googleLogin,
	googleCallback,
	getUser,
};
