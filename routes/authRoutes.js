const express = require('express');
const passport = require('passport'); // Import passport
const {
	registerUser,
	loginUser,
	verifyEmail,
	getAllUsers,
	setAdmin,
	forgotPassword,
	resetPassword,
	checkUserStatus,
	googleLogin,
	googleCallback,
	getUser,
	resendVerificationCode,
} = require('../controllers/authController');
const authMiddleware = require('../middleware/authMiddleware');
const validate = require('../middleware/validationMiddleware');
const {
	registerUserSchema,
	loginUserSchema,
	verifyEmailSchema,
	forgotPasswordSchema,
	resetPasswordSchema,
} = require('../validation/schemas');

const router = express.Router();

router.post('/register', registerUser);
router.post('/login', loginUser);
router.post('/verify-email', verifyEmail);
router.get('/user', getUser);
router.post('/resend-verification', resendVerificationCode);
// Forgot Password Route
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', resetPassword);
router.get('/status', checkUserStatus);

// Google Auth Routes
router.get('/google', googleLogin);
router.get('/google/callback', googleCallback);

module.exports = router;
